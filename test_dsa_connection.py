"""
测试DSA连接和数据收集
用于诊断初始化数据收集问题
"""

import asyncio
import time
from data_collector_3s import DSADataCollector

async def test_dsa_basic_collection():
    """测试基本的DSA数据收集功能"""
    dll_path = r"SDK发布20200723\x64\DSANetSDK.dll"
    
    print("="*50)
    print("DSA连接和数据收集测试")
    print("="*50)
    
    try:
        # 创建数据收集器
        print("1. 创建DSA数据收集器...")
        collector = DSADataCollector(dll_path)
        
        # 启用连续模式和实时数据流
        print("2. 启用连续模式和实时数据流...")
        collector.enable_continuous_mode()
        collector.enable_real_time_streaming()
        
        # 初始化SDK
        print("3. 初始化SDK...")
        if not collector.initialize_sdk():
            print("❌ SDK初始化失败")
            return False
        
        # 启动数据收集
        print("4. 启动数据收集...")
        if not collector.start_collection():
            print("❌ 数据收集启动失败")
            return False
        
        print("5. 等待数据收集稳定...")
        await asyncio.sleep(3.0)
        
        # 检查初始状态
        status = collector.get_collection_status()
        print(f"初始状态: {status}")
        
        # 尝试收集一些数据点
        print("6. 开始收集测试数据...")
        collected_data = []
        max_attempts = 1000
        
        for attempt in range(max_attempts):
            latest_data = collector.get_latest_data()
            
            if latest_data['displacement'] is not None:
                collected_data.append(latest_data['displacement'])
                collector.latest_displacement = None  # 标记为已消费
                print(f"收集到位移数据 #{len(collected_data)}: {latest_data['displacement']:.6f} μm")
                
                if len(collected_data) >= 10:  # 收集10个数据点就够了
                    break
            
            if attempt % 100 == 0:
                status = collector.get_collection_status()
                print(f"尝试 {attempt}: 总数据点={status['total_data_points']}, 最新位移={status['latest_displacement']}")
            
            await asyncio.sleep(0.01)
        
        print(f"\n7. 测试结果:")
        print(f"   收集到的数据点数: {len(collected_data)}")
        print(f"   总尝试次数: {attempt + 1}")
        
        if len(collected_data) > 0:
            print(f"   数据范围: {min(collected_data):.6f} ~ {max(collected_data):.6f} μm")
            print("✅ DSA数据收集正常工作")
            success = True
        else:
            print("❌ 未收集到任何数据")
            success = False
        
        # 停止收集
        print("8. 停止数据收集...")
        collector.stop_collection()
        
        return success
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False

async def test_dsa_with_bridge():
    """测试通过bridge的数据收集"""
    from real_time_data_bridge import DSADataSource
    
    print("\n" + "="*50)
    print("通过Bridge测试DSA数据收集")
    print("="*50)
    
    dll_path = r"SDK发布20200723\x64\DSANetSDK.dll"
    
    try:
        # 创建数据源
        print("1. 创建DSA数据源...")
        displacement_source = DSADataSource(dll_path, 'displacement', enable_continuous_mode=True)
        
        # 启动数据收集
        print("2. 启动数据收集...")
        if not displacement_source.start_collection():
            print("❌ 数据收集启动失败")
            return False
        
        print("3. 等待数据收集稳定...")
        await asyncio.sleep(5.0)
        
        # 尝试读取数据
        print("4. 开始读取数据...")
        collected_data = []
        max_attempts = 1000
        
        for attempt in range(max_attempts):
            data = await displacement_source.read_data()
            if data is not None:
                collected_data.append(data)
                print(f"读取到数据 #{len(collected_data)}: {data:.6f} μm")
                
                if len(collected_data) >= 10:
                    break
            
            if attempt % 100 == 0:
                print(f"尝试 {attempt}: 已收集 {len(collected_data)} 个数据点")
            
            await asyncio.sleep(0.01)
        
        print(f"\n5. 测试结果:")
        print(f"   收集到的数据点数: {len(collected_data)}")
        print(f"   总尝试次数: {attempt + 1}")
        
        if len(collected_data) > 0:
            print(f"   数据范围: {min(collected_data):.6f} ~ {max(collected_data):.6f} μm")
            print("✅ Bridge数据收集正常工作")
            success = True
        else:
            print("❌ 未收集到任何数据")
            success = False
        
        # 停止收集
        print("6. 停止数据收集...")
        displacement_source.stop_collection()
        
        return success
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False

async def main():
    """主测试函数"""
    print("开始DSA连接诊断测试...\n")
    
    # 测试1: 基本数据收集
    result1 = await test_dsa_basic_collection()
    
    # 测试2: 通过Bridge的数据收集
    result2 = await test_dsa_with_bridge()
    
    print("\n" + "="*50)
    print("测试总结")
    print("="*50)
    print(f"基本数据收集: {'✅ 通过' if result1 else '❌ 失败'}")
    print(f"Bridge数据收集: {'✅ 通过' if result2 else '❌ 失败'}")
    
    if result1 and result2:
        print("\n🎉 所有测试通过！DSA数据收集功能正常")
        print("建议检查 integrated_real_time_demo.py 中的初始化等待时间设置")
    else:
        print("\n⚠️  存在问题，请检查：")
        print("   1. DSA设备是否正确连接")
        print("   2. SDK DLL文件是否存在")
        print("   3. 设备驱动是否正确安装")
        print("   4. 网络设置是否正确")

if __name__ == "__main__":
    asyncio.run(main())
