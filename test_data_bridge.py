"""
测试数据桥接功能
验证DSA数据收集器和数据源之间的数据传递
"""

import asyncio
import time
from real_time_data_bridge import DSADataSource

async def test_data_bridge():
    """测试数据桥接功能"""
    dll_path = r"SDK发布20200723\x64\DSANetSDK.dll"
    
    print("="*60)
    print("测试DSA数据桥接功能")
    print("="*60)
    
    try:
        # 创建位移数据源
        print("1. 创建位移数据源...")
        displacement_source = DSADataSource(dll_path, 'displacement', enable_continuous_mode=True)
        
        # 启动数据收集
        print("2. 启动位移数据收集...")
        if not displacement_source.start_collection():
            print("❌ 位移数据收集启动失败")
            return False
        
        print("3. 等待数据收集稳定...")
        await asyncio.sleep(5.0)
        
        # 检查数据收集器状态
        if displacement_source.collector:
            status = displacement_source.collector.get_collection_status()
            print(f"数据收集器状态: {status}")
        
        # 测试数据读取
        print("4. 开始测试数据读取...")
        collected_data = []
        max_attempts = 2000  # 增加尝试次数
        successful_reads = 0
        
        for attempt in range(max_attempts):
            data = await displacement_source.read_data()
            if data is not None:
                collected_data.append(data)
                successful_reads += 1
                
                if len(collected_data) >= 25:  # 收集25个数据点
                    break
            
            # 每500次尝试显示一次状态
            if attempt % 500 == 0 and attempt > 0:
                print(f"尝试 {attempt}: 成功读取 {successful_reads} 次, 收集到 {len(collected_data)} 个数据点")
                if displacement_source.collector:
                    latest_data = displacement_source.collector.get_latest_data()
                    print(f"最新数据: 位移={latest_data['displacement']}, 时间戳={latest_data['displacement_time']}")
            
            await asyncio.sleep(0.005)  # 5ms间隔
        
        print(f"\n5. 测试结果:")
        print(f"   总尝试次数: {attempt + 1}")
        print(f"   成功读取次数: {successful_reads}")
        print(f"   收集到的数据点数: {len(collected_data)}")
        print(f"   成功率: {successful_reads / (attempt + 1) * 100:.2f}%")
        
        if len(collected_data) >= 25:
            print(f"   数据范围: {min(collected_data):.6f} ~ {max(collected_data):.6f} μm")
            print("✅ 数据桥接测试成功！")
            success = True
        elif len(collected_data) > 0:
            print(f"   数据范围: {min(collected_data):.6f} ~ {max(collected_data):.6f} μm")
            print("⚠️  收集到部分数据，但不足25个点")
            success = False
        else:
            print("❌ 未收集到任何数据")
            success = False
        
        # 停止收集
        print("6. 停止数据收集...")
        displacement_source.stop_collection()
        
        return success
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_velocity_bridge():
    """测试速度数据桥接功能"""
    dll_path = r"SDK发布20200723\x64\DSANetSDK.dll"
    
    print("\n" + "="*60)
    print("测试速度数据桥接功能")
    print("="*60)
    
    try:
        # 创建速度数据源
        print("1. 创建速度数据源...")
        velocity_source = DSADataSource(dll_path, 'velocity', enable_continuous_mode=True)
        
        # 启动数据收集
        print("2. 启动速度数据收集...")
        if not velocity_source.start_collection():
            print("❌ 速度数据收集启动失败")
            return False
        
        print("3. 等待数据收集稳定...")
        await asyncio.sleep(3.0)
        
        # 测试数据读取
        print("4. 开始测试速度数据读取...")
        collected_data = []
        max_attempts = 1000
        
        for attempt in range(max_attempts):
            data = await velocity_source.read_data()
            if data is not None:
                collected_data.append(data)
                
                if len(collected_data) >= 10:  # 收集10个数据点
                    break
            
            await asyncio.sleep(0.01)
        
        print(f"\n5. 速度数据测试结果:")
        print(f"   收集到的数据点数: {len(collected_data)}")
        
        if len(collected_data) > 0:
            print(f"   数据范围: {min(collected_data):.6f} ~ {max(collected_data):.6f} μm/s")
            print("✅ 速度数据桥接测试成功！")
            success = True
        else:
            print("❌ 未收集到任何速度数据")
            success = False
        
        # 停止收集
        print("6. 停止速度数据收集...")
        velocity_source.stop_collection()
        
        return success
        
    except Exception as e:
        print(f"❌ 速度测试过程中发生错误: {e}")
        return False

async def main():
    """主测试函数"""
    print("开始数据桥接测试...\n")
    
    # 测试位移数据桥接
    result1 = await test_data_bridge()
    
    # 等待一下再测试速度数据
    await asyncio.sleep(2.0)
    
    # 测试速度数据桥接
    result2 = await test_velocity_bridge()
    
    print("\n" + "="*60)
    print("测试总结")
    print("="*60)
    print(f"位移数据桥接: {'✅ 通过' if result1 else '❌ 失败'}")
    print(f"速度数据桥接: {'✅ 通过' if result2 else '❌ 失败'}")
    
    if result1:
        print("\n🎉 位移数据桥接测试通过！")
        print("现在可以运行 integrated_real_time_demo.py 进行完整测试")
    else:
        print("\n⚠️  位移数据桥接存在问题")
        print("建议检查:")
        print("  1. 数据时间戳是否正确更新")
        print("  2. 数据读取逻辑是否正确")
        print("  3. DSA回调频率与读取频率的匹配")

if __name__ == "__main__":
    asyncio.run(main())
