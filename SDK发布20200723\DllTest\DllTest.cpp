// DllTest.cpp : 定义控制台应用程序的入口点。
//

#include "stdafx.h"
#include <windows.h>
#include <functional>  

#ifdef WIN64
#include "../x64/DSANetSDK.h"
	#ifdef _DEBUG
	#pragma comment(lib, "../x64/DSANetSDK.lib")   
	#else
	#pragma comment(lib, "../x64/DSANetSDK.lib")   
	#endif

#else
#include "../x86/DSANetSDK.h"
	#ifdef _DEBUG
	#pragma comment(lib, "../x86/DSANetSDK.lib")   
	#else
	#pragma comment(lib, "../x86/DSANetSDK.lib")   
	#endif
#endif
using namespace DSANetSDK;

void dataCallBack(OutDataType dataType,  int sRate, VelocityRange vRange, DisplacementRange dRange,
	float data[], int dataLen)
{

	printf("OutDataType %d SampleRate %d VelocityRange %d DisplacementRange %d  datalen %d\n", dataType, sRate, vRange, dRange, dataLen);
}

int main()
{
	printf("DSANet SDK 测试程序开始\n");
	printf("========================\n");

	//dataCallFun functional = std::bind(dataCallBack, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3, std::placeholders::_4, std::placeholders::_5, std::placeholders::_6);

	int ret = 0;
	//dll载入后必须先调用此方法初始化  调用一次即可
	printf("正在初始化...\n");
	ret = initialize();
	printf("初始化结果: %d\n", ret);
	if (ret != 0)
		printf("initialize error %d\n", ret);

	//必须开始前调用 需要再次设置 先暂停再设置⬇⬇⬇⬇⬇⬇⬇⬇⬇⬇⬇⬇⬇⬇⬇⬇⬇⬇⬇⬇
	//0.0.0.0 是绑定本地所有  但是多网卡的时候由于静态路由表的问题 可能会接收不到数据
	//如果绑定指定网卡 设置那个指定网卡的ip  比如说************
	if(setBindInfo((char*)"0.0.0.0", 62618)!=0)
		printf("setBindInfo error %d\n", ret);
	//广播地址 默认***************就行
	if (setBoardcastInfo((char*)"***************", 63082) != 0)
		printf("setBoardcastInfo error %d\n", ret);

	//默认就行
	if (setDeviceType(DeviceType::DT3M) != 0)
		printf("setDeviceType error %d\n", ret);
	//默认就行
	if (setDeviceId(0) != 0)
		printf("setDeviceId error %d\n", ret);
	
	//设置及激光器的波长  氦氖632.8  光纤1550
	if (setLaserWaveLength(LaserWaveLength::LW_632_8))
		printf("setLaserWaveLength error %d\n", ret);

	//设置数据回调
	if (setDataCallBack(dataCallBack)!=0)
	{
		printf("setDataCallBack error %d\n", ret);
	}
	//设置每次回调的数据长度
	if (setDataLength(1024) != 0)
		printf("setDataLength error %d\n", ret);
	//必须开始前调用 需要再次设置 先暂停再设置⬆⬆⬆⬆⬆⬆⬆⬆⬆⬆⬆⬆⬆⬆⬆⬆⬆⬆⬆⬆⬆⬆⬆⬆


	
	//随时可以调用
	//设置输出滤波
	if (setOutputFilter(OutputFilter::of2k) != 0)
		printf("setOutputFilter error %d\n", ret);
	//设置速度档位
	if (setVelocityRange(VelocityRange::vr1) != 0)
		printf("setVelocityRange error %d\n", ret);
	//设置位移档位
	if (setDisplacementRange(DisplacementRange::dr1) != 0)
		printf("setDisplacementRange error %d\n", ret);
	//设置返回数据类型
	if (setOutDataType(OutDataType::odtNoOutput) != 0)
		printf("setOutDataType error %d\n", ret);
	Sleep(50);
	//设置位移输出  输出滤波1M以上档位只支持一路输出
	if (setOutDataType(OutDataType::odtDisplacement) != 0)
		printf("setOutDataType error %d\n", ret);
	//随时可以调用



	//开始读取数据
	printf("正在启动测量...\n");
	ret = start();
	printf("启动结果: %d\n", ret);
	if (ret != 0)
		printf("start error %d\n", ret);

	printf("程序运行中，按任意键停止...\n");
	//Sleep(10000);
	system("pause");
	//停止读取数据
	if (stop() != 0)
		printf("stop error %d\n", ret);

	//程序退出调用  调用一次即可
	if (unInitialize() != 0)
		printf("unInitialize error %d\n", ret);

	return 0;
}

