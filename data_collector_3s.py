import ctypes
from ctypes import *
import time
import os
import queue
import matplotlib.pyplot as plt

class DSADataCollector:
    def __init__(self, dll_path):
        """初始化数据收集器"""
        if not os.path.exists(dll_path):
            raise FileNotFoundError(dll_path)
        self.sdk = ctypes.cdll.LoadLibrary(dll_path)

        # 定义回调函数类型
        self.CALLBACK = CFUNCTYPE(None, c_int, c_int, c_int, c_int, POINTER(c_float), c_int)
        self._callback_func = self.CALLBACK(self._data_callback)

        # 数据缓冲区
        self.training_data = []  # 原始训练数据
        self.start_time = None
        self.is_running = True
        self.continuous_mode = False  # 连续模式标志

        # 实时数据流
        self.real_time_queue = queue.Queue(maxsize=10000)
        self.enable_real_time_stream = True

        # 最新数据缓存（用于实时预测）
        self.latest_displacement = None
        self.latest_velocity = None
        self.latest_displacement_time = None
        self.latest_velocity_time = None

        # SDK 枚举
        self.OutputFilter = {'of1k':0x00,'of2k':0x01,'of10k':0x02,'of20k':0x03}
        self.OutDataType = {'odtVelocity':0x01,'odtDisplacement':0x02,'odtAll':0x03,'odtNoOutput':0x04}
        self.VelocityRange = {'vr1':0x01}
        self.DisplacementRange = {'dr1':0x01}
        self.DeviceType = {'DT3M':0x01}
        self.LaserWaveLength = {'LW_632_8':0x01}

    def _data_callback(self, dataType, sRate, vRange, dRange, data_ptr, dataLen):
        """SDK 数据回调"""
        if not self.is_running:
            return

        # 将指针转换为数组
        array_type = c_float * dataLen
        data = cast(data_ptr, POINTER(array_type)).contents
        values = list(data)

        # 初始化开始时间
        current_time = time.time()
        if self.start_time is None:
            self.start_time = current_time
            print("开始收集数据...")

        # 检查运行时间或数据点数（如果不是连续模式）
        elapsed_time = current_time - self.start_time
        if not self.continuous_mode:
            # 目标收集300000个原始数据点（每10个点取1个，得到30000个训练数据点）
            target_raw_points = 300000
            if len(self.training_data) >= target_raw_points:
                self.is_running = False
                print(f"\n已收集{len(self.training_data)}个数据点，停止收集")
                return
            # 或者时间超过30秒也停止（300000点 ÷ 10000Hz = 30秒）
            elif elapsed_time >= 30.0:
                self.is_running = False
                print(f"\n已运行30秒，停止收集")
                return

        # SDK 实际采样率
        effective_sRate = sRate if sRate > 0 else 10000  # 默认 10000Hz

        # 收集训练数据并放入实时队列
        for i, value in enumerate(values):
            timestamp_ms = (elapsed_time + i / effective_sRate) * 1000  # 相对时间, ms
            self.training_data.append({'timestamp_ms': timestamp_ms, 'value': value})
            if self.enable_real_time_stream:
                try:
                    self.real_time_queue.put_nowait({'timestamp_ms': timestamp_ms, 'value': value})
                except queue.Full:
                    # 队列满时，丢弃最旧的数据，添加新数据
                    try:
                        self.real_time_queue.get_nowait()  # 移除最旧的数据
                        self.real_time_queue.put_nowait({'timestamp_ms': timestamp_ms, 'value': value})
                    except queue.Empty:
                        pass  # 队列为空，忽略

        # 更新最新数据缓存（用于实时预测）
        if len(values) > 0:
            latest_value = values[-1]  # 取最后一个值作为最新值
            latest_timestamp = (elapsed_time + (len(values) - 1) / effective_sRate) * 1000

            if dataType == self.OutDataType['odtDisplacement']:  # 位移数据
                self.latest_displacement = latest_value
                self.latest_displacement_time = latest_timestamp
                # 调试输出 - 显示前几次更新
                if len(self.training_data) <= 10 or len(self.training_data) % 1000 == 0:
                    print(f"更新最新位移数据: {latest_value:.6f} μm, 时间戳: {latest_timestamp:.3f}")
            elif dataType == self.OutDataType['odtVelocity']:  # 速度数据
                self.latest_velocity = latest_value
                self.latest_velocity_time = latest_timestamp
                # 调试输出 - 显示前几次更新
                if len(self.training_data) <= 10 or len(self.training_data) % 1000 == 0:
                    print(f"更新最新速度数据: {latest_value:.6f} μm/s, 时间戳: {latest_timestamp:.3f}")

        # 每收到一批数据后，打印调试信息
        if len(values) > 0:
            # 创建数据类型映射
            data_type_names = {1: '速度', 2: '位移', 3: '全部'}
            data_type_name = data_type_names.get(dataType, '未知')
            print(f"接收到{data_type_name}数据: {len(values)}个点, 数据类型码: {dataType}, SDK传入dataLen: {dataLen}, 采样率: {sRate}Hz")
            if len(self.training_data) % 1000 == 0:
                print(f"已收集训练数据: {len(self.training_data)} 个点")

    def initialize_sdk(self):
        """初始化 SDK"""
        print("初始化SDK...")
        result = self.sdk.initialize()
        if result != 0:
            print(f"初始化失败: {result}")
            return False
        
        self.sdk.setBindInfo(b"0.0.0.0", 62618)
        self.sdk.setBoardcastInfo(b"255.255.255.255", 63082)
        self.sdk.setDeviceType(self.DeviceType['DT3M'])
        self.sdk.setDeviceId(0)
        self.sdk.setLaserWaveLength(self.LaserWaveLength['LW_632_8'])
        self.sdk.setDataCallBack(self._callback_func)
        self.sdk.setDataLength(128)  # 每次回调128点
        self.sdk.setOutputFilter(self.OutputFilter['of10k'])  # 设置为10000Hz采样
        self.sdk.setVelocityRange(self.VelocityRange['vr1'])
        self.sdk.setDisplacementRange(self.DisplacementRange['dr1'])
        self.sdk.setOutDataType(self.OutDataType['odtAll'])  # 收集位移和速度数据
        
        print("SDK初始化完成")
        return True

    def start_collection(self):
        """启动数据采集"""
        print("启动数据采集...")
        result = self.sdk.start()
        if result != 0:
            print(f"启动失败: {result}")
            return False
        return True

    def stop_collection(self):
        """停止采集"""
        self.sdk.stop()
        self.sdk.unInitialize()
        print("采集停止，SDK已反初始化")

    def save_training_data(self):
        """保存训练数据到 v1.txt，均匀从每10个点取1个，使用3秒内收集的所有数据"""
        filename = "v1.txt"
        with open(filename, 'w', encoding='utf-8') as f:
            f.write("时间[ms]\t位移[μm]\n")
            total_points = len(self.training_data)
            step = 10  # 每10个点取1个

            # 使用3秒内收集到的所有数据
            for i in range(0, total_points, step):
                point = self.training_data[i]
                f.write(f"{point['timestamp_ms']:.3f}\t{point['value']:.6f}\n")

            actual_training_points = total_points // step
            print(f"训练数据已保存到 {filename}")
            print(f"原始数据点数: {total_points}")
            print(f"降采样后训练数据点数: {actual_training_points}")
            print(f"数据时长: {total_points / 10000:.1f} 秒")

    def plot_training_data(self):
        """绘制v1.txt中的位移曲线"""
        timestamps = [d['timestamp_ms'] for d in self.training_data[::10]]  # 每10个取1
        values = [d['value'] for d in self.training_data[::10]]

        # 设置中文字体支持
        try:
            plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
            plt.rcParams['axes.unicode_minus'] = False
        except:
            pass  # 如果字体设置失败，使用默认字体

        plt.figure(figsize=(12, 6))
        plt.plot(timestamps, values, color='blue', linewidth=1, alpha=0.8)
        plt.title("Displacement vs Time Curve", fontsize=14)
        plt.xlabel("Time [ms]", fontsize=12)
        plt.ylabel("Displacement [μm]", fontsize=12)
        plt.grid(True, alpha=0.3)
        plt.tight_layout()

        # 保存图片而不是直接显示（避免GUI问题）
        try:
            plt.savefig('displacement_curve.png', dpi=150, bbox_inches='tight')
            print("位移曲线图已保存为 displacement_curve.png")
            plt.close()
        except Exception as e:
            print(f"保存图片失败: {e}")
            plt.close()

    def get_latest_data(self):
        """获取最新的位移和速度数据

        返回:
            dict: {
                'displacement': float or None,
                'velocity': float or None,
                'displacement_time': float or None,
                'velocity_time': float or None
            }
        """
        return {
            'displacement': self.latest_displacement,
            'velocity': self.latest_velocity,
            'displacement_time': self.latest_displacement_time,
            'velocity_time': self.latest_velocity_time
        }

    def has_new_displacement(self):
        """检查是否有新的位移数据"""
        return self.latest_displacement is not None

    def has_new_velocity(self):
        """检查是否有新的速度数据"""
        return self.latest_velocity is not None

    def clear_latest_data(self):
        """清空最新数据缓存（用于标记数据已被消费）"""
        self.latest_displacement = None
        self.latest_velocity = None
        self.latest_displacement_time = None
        self.latest_velocity_time = None

    def get_collection_status(self):
        """获取数据收集状态"""
        return {
            'is_running': self.is_running,
            'continuous_mode': self.continuous_mode,
            'total_data_points': len(self.training_data),
            'latest_displacement': self.latest_displacement,
            'latest_velocity': self.latest_velocity,
            'queue_size': self.real_time_queue.qsize() if hasattr(self.real_time_queue, 'qsize') else 0
        }

    def run(self):
        """运行收集"""
        if not self.initialize_sdk():
            return False
        if not self.start_collection():
            return False
        
        print("\n开始收集前3秒数据...")
        try:
            while self.is_running:
                time.sleep(0.05)
        except KeyboardInterrupt:
            print("用户中断采集")
            self.is_running = False
        finally:
            self.stop_collection()
            self.save_training_data()
            self.plot_training_data()  # 绘图
        print("数据收集完成")
        return True

    def enable_real_time_streaming(self):
        """启用实时数据流"""
        self.enable_real_time_stream = True
        print("实时数据流已启用")

    def disable_real_time_streaming(self):
        """禁用实时数据流"""
        self.enable_real_time_stream = False
        print("实时数据流已禁用")

    def enable_continuous_mode(self):
        """启用连续模式：训练完成后继续运行进行预测"""
        self.continuous_mode = True
        print("连续模式已启用：将持续运行直到手动停止")

    def get_real_time_data_by_type(self, data_type, timeout=0.001):
        """按类型获取实时数据（非阻塞，不会消耗其他类型队列）"""
        try:
            # 在这个简化版本中，只有一个队列，所以直接返回
            # data_type 参数在此版本中暂未使用，但保留接口兼容性
            return self.real_time_queue.get(timeout=timeout)
        except queue.Empty:
            return None

    def get_real_time_queue_size(self):
        """获取实时队列大小"""
        return self.real_time_queue.qsize()

    def get_training_data_status(self):
        """获取训练数据收集状态"""
        current_count = len(self.training_data)
        target_raw_points = 300000  # 目标收集300000个原始数据点

        if self.start_time is None:
            elapsed_time = 0.0
            collected = False
        else:
            elapsed_time = time.time() - self.start_time
            collected = not self.is_running  # 如果停止运行，说明收集完成

        progress = min(100.0, (current_count / target_raw_points) * 100.0) if current_count > 0 else 0.0

        # 计算预期的训练数据点数（每10个原始数据点取1个）
        expected_training_points = current_count // 10

        return {
            'count': current_count,
            'target': target_raw_points,
            'progress': progress,
            'collected': collected,
            'elapsed_time': elapsed_time,
            'expected_training_points': expected_training_points
        }


def main():
    dll_path = r"SDK发布20200723\x64\DSANetSDK.dll"
    if not os.path.exists(dll_path):
        print(f"错误：找不到DLL文件 {dll_path}")
        return

    collector = DSADataCollector(dll_path)
    collector.run()


if __name__ == "__main__":
    main()
