"""
实时数据桥接器
连接 data_collector_3s.py 和 real_time_visualization_demo.py
"""

import asyncio
import time
import threading
import os
from data_collector_3s import DSADataCollector


class DSADataSource:
    """DSA数据收集器的数据源适配器"""
    
    def __init__(self, dll_path, data_type='displacement', enable_continuous_mode=True):
        """
        初始化DSA数据源

        Args:
            dll_path: DSA SDK DLL文件路径
            data_type: 数据类型 ('displacement' 或 'velocity')
            enable_continuous_mode: 是否启用连续模式（训练数据收集时应为False）
        """
        self.dll_path = dll_path
        self.data_type = data_type
        self.enable_continuous_mode = enable_continuous_mode
        self.collector = None
        self.is_connected = False
        self.collection_thread = None
        self.last_data_time = 0
        self.data_count = 0
        
    def start_collection(self):
        """启动数据收集"""
        if self.is_connected:
            print("数据收集已经在运行")
            return True
            
        # 检查DLL文件
        if not os.path.exists(self.dll_path):
            print(f"错误：找不到DLL文件 {self.dll_path}")
            return False
        
        try:
            # 创建数据收集器
            self.collector = DSADataCollector(self.dll_path)
            
            # 启用实时数据流
            self.collector.enable_real_time_streaming()
            # 根据参数决定是否启用连续模式
            if self.enable_continuous_mode:
                self.collector.enable_continuous_mode()
                print("已启用连续模式：将持续运行直到手动停止")
            else:
                print("未启用连续模式：将在收集完训练数据后自动停止")
            
            # 在单独线程中启动数据收集
            self.collection_thread = threading.Thread(
                target=self._run_collector, 
                daemon=True
            )
            self.collection_thread.start()
            
            # 等待一小段时间确保收集器启动
            time.sleep(0.5)
            
            self.is_connected = True
            print(f"DSA数据收集器已启动，数据类型: {self.data_type}")
            return True
            
        except Exception as e:
            print(f"启动数据收集器失败: {e}")
            return False
    
    def _run_collector(self):
        """在单独线程中运行数据收集器"""
        try:
            self.collector.run()
        except Exception as e:
            print(f"数据收集器运行错误: {e}")
            self.is_connected = False
    
    async def read_data(self):
        """读取最新数据（异步接口）"""
        if not self.is_connected or not self.collector:
            return None

        # 获取最新数据
        try:
            latest_data = self.collector.get_latest_data()

            if self.data_type == 'displacement':
                if latest_data['displacement'] is not None:
                    # 返回最新位移数据，并清空缓存表示已消费
                    displacement = latest_data['displacement']
                    self.collector.latest_displacement = None  # 标记为已消费

                    self.data_count += 1
                    self.last_data_time = time.time()

                    # 每100个数据点显示一次统计（降低频率）
                    if self.data_count % 100 == 0:
                        print(f"已读取 {self.data_count} 个{self.data_type}数据点")

                    return displacement

            elif self.data_type == 'velocity':
                if latest_data['velocity'] is not None:
                    # 返回最新速度数据，并清空缓存表示已消费
                    velocity = latest_data['velocity']
                    self.collector.latest_velocity = None  # 标记为已消费

                    self.data_count += 1
                    self.last_data_time = time.time()

                    # 每100个数据点显示一次统计（降低频率）
                    if self.data_count % 100 == 0:
                        print(f"已读取 {self.data_count} 个{self.data_type}数据点")

                    return velocity

            return None

        except Exception as e:
            return None
    
    def stop_collection(self):
        """停止数据收集"""
        if self.collector:
            self.collector.is_running = False
            self.collector.disable_real_time_streaming()
        
        self.is_connected = False
        print("DSA数据收集已停止")
    
    def is_connected_status(self):
        """检查连接状态"""
        return self.is_connected and (self.collector is not None)
    
    def get_stats(self):
        """获取统计信息"""
        if not self.collector:
            return {}
        
        return {
            'data_count': self.data_count,
            'queue_size': self.collector.get_real_time_queue_size(),
            'last_data_time': self.last_data_time,
            'data_type': self.data_type
        }


class RealTimeVisualizationBridge:
    """实时可视化桥接器"""
    
    def __init__(self, dll_path):
        self.dll_path = dll_path
        self.displacement_source = None
        self.velocity_source = None
        
    async def start_displacement_visualization(self, for_training=False):
        """启动位移数据的实时可视化

        Args:
            for_training: 是否用于训练数据收集（如果是，则不启用连续模式）
        """
        if for_training:
            print("启动位移数据收集（训练模式）...")
        else:
            print("启动位移数据实时可视化...")

        # 创建位移数据源，训练模式下不启用连续模式
        self.displacement_source = DSADataSource(
            self.dll_path,
            'displacement',
            enable_continuous_mode=not for_training
        )

        if not self.displacement_source.start_collection():
            print("启动位移数据收集失败")
            return False

        # 等待数据收集稳定
        await asyncio.sleep(2.0)

        if for_training:
            print("位移数据源已准备就绪（训练模式）")
        else:
            print("位移数据源已准备就绪")
        return True
    
    async def start_velocity_visualization(self):
        """启动速度数据的实时可视化"""
        print("启动速度数据实时可视化...")

        # 创建速度数据源
        self.velocity_source = DSADataSource(self.dll_path, 'velocity')

        if not self.velocity_source.start_collection():
            print("启动速度数据收集失败")
            return False

        # 等待数据收集稳定
        await asyncio.sleep(2.0)

        print("速度数据源已准备就绪")
        return True
    
    def get_displacement_source(self):
        """获取位移数据源"""
        return self.displacement_source
    
    def get_velocity_source(self):
        """获取速度数据源"""
        return self.velocity_source
    
    def stop_all(self):
        """停止所有数据收集"""
        if self.displacement_source:
            self.displacement_source.stop_collection()
        if self.velocity_source:
            self.velocity_source.stop_collection()
        print("所有数据收集已停止")


# 使用示例
async def test_bridge():
    """测试桥接器"""
    dll_path = r"SDK发布20200723\x64\DSANetSDK.dll"
    
    # 创建桥接器
    bridge = RealTimeVisualizationBridge(dll_path)
    
    # 启动位移数据可视化
    if await bridge.start_displacement_visualization():
        displacement_source = bridge.get_displacement_source()
        
        print("开始读取实时位移数据...")
        for i in range(100):  # 读取100个数据点进行测试
            data = await displacement_source.read_data()
            if data is not None:
                print(f"位移数据 {i+1}: {data:.6f} μm")
            await asyncio.sleep(0.01)  # 100Hz采样
    
    # 停止收集
    bridge.stop_all()


if __name__ == "__main__":
    print("实时数据桥接器测试")
    asyncio.run(test_bridge())
