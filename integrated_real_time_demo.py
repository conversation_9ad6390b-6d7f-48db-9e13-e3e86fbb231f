"""
集成实时可视化演示
使用 data_collector_3s.py 的实时数据作为 real_time_visualization_demo.py 的输入
"""

import asyncio
import time
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
from sklearn.preprocessing import MinMaxScaler
import matplotlib.pyplot as plt
import warnings
from collections import deque
import os

# 导入我们的模块
from real_time_stream_processor import RealTimeStreamProcessor
from real_time_data_bridge import DSADataSource, RealTimeVisualizationBridge
from real_time_train import train_displacement_model    
from real_time_visualization_demo import AdaptiveKalmanFilter
from plot_displacement import load_data as plot_load_data, plot_displacement_comparison, plot_error_analysis, calculate_metrics as plot_calculate_metrics

warnings.filterwarnings("ignore")

# 设置中文字体
try:
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    plt.rcParams['font.family'] = 'sans-serif'
    print("中文字体设置成功: SimHei")
except:
    plt.rcParams['axes.unicode_minus'] = False
    print("使用默认字体")


async def collect_one_second_data(processor, displacement_source, velocity_source, second_number):
    """收集一秒的位移、速度和预测数据"""
    print(f"📊 开始收集第{second_number}秒数据...")

    # 数据存储
    data_records = []
    start_time = time.time()

    # 收集1秒数据
    duration = 1.0  # 1秒
    target_samples = 5000  # 目标5000个样本点（5000Hz × 1秒）

    sample_count = 0
    last_displacement = None
    last_velocity = None

    print(f"  目标: {target_samples} 个数据点（2000Hz × 1秒）")
    print(f"  预测策略: 每个数据点都进行实际预测")

    while time.time() - start_time < duration and len(data_records) < target_samples:
        current_time = time.time() - start_time

        # 更频繁地尝试读取数据
        for _ in range(10):  # 每次循环尝试读取10次
            # 读取位移数据
            displacement_data = await displacement_source.read_data()
            if displacement_data is not None:
                last_displacement = displacement_data

            # 读取速度数据
            velocity_data = await velocity_source.read_data()
            if velocity_data is not None:
                last_velocity = velocity_data

            # 如果有新的位移数据，进行预测和记录
            if last_displacement is not None:
                # 检查输入数据范围（仅在第一个数据点时显示）
                if len(data_records) == 0:
                    print(f"  📊 实时数据范围检查: 输入位移 = {last_displacement:.6f} μm")

                # 进行预测
                prediction_start = time.time()
                prediction = processor.process_single_point(last_displacement)
                processing_time = time.time() - prediction_start

                if prediction is not None:
                    # 在第一个预测时显示预测值范围
                    if len(data_records) == 0:
                        print(f"  📊 预测结果检查: 预测位移 = {prediction:.6f} μm")

                    # 记录数据（时间相对于整个3秒周期）
                    record = {
                        'time': (second_number - 1) + current_time,  # 第1秒: 0-1s, 第2秒: 1-2s, 第3秒: 2-3s
                        'displacement': last_displacement,
                        'velocity': last_velocity if last_velocity is not None else 0.0,
                        'prediction': prediction,
                        'processing_time': processing_time
                    }
                    data_records.append(record)

                    # 重置位移数据，避免重复记录
                    last_displacement = None
                    break

            # 短暂等待 - 优化：减少等待时间以提高吞吐量
            await asyncio.sleep(0.00001)  # 10μs 而不是 100μs

        sample_count += 1

        # 动态调整采样间隔 - 优化：更激进的采样策略
        if len(data_records) > 0:
            current_rate = len(data_records) / current_time if current_time > 0 else 0
            if current_rate < 1600:  # 如果速率低于1600Hz，减少等待时间
                await asyncio.sleep(0.00001)  # 10μs
            else:
                await asyncio.sleep(0.00001)  # 保持一致的低延迟
        else:
            await asyncio.sleep(0.00001)  # 10μs

    actual_duration = time.time() - start_time
    actual_rate = len(data_records) / actual_duration if actual_duration > 0 else 0

    print(f"  ✅ 第{second_number}秒完成: {len(data_records)} 个数据点, 实际速率: {actual_rate:.1f} Hz")

    return data_records


async def collect_three_seconds_data_by_second(processor, displacement_source, velocity_source):
    """按秒分别收集三秒的位移、速度和预测数据"""
    print("🚀 训练完成！开始计时收集数据...")
    print("⏰ 正式开始计时 - 收集第1、2、3秒数据")
    print("="*60)

    all_data_records = []

    # 分别收集第1、2、3秒的数据
    for second in range(1, 4):
        print(f"\n📍 第{second}秒数据收集:")
        second_data = await collect_one_second_data(processor, displacement_source, velocity_source, second)
        all_data_records.extend(second_data)

        # 显示当前进度
        total_collected = len(all_data_records)
        target_total = 6000  # 3秒 × 2000Hz
        progress = (total_collected / target_total) * 100
        print(f"  📈 累计收集: {total_collected} 个数据点 ({progress:.1f}%)")

    print("\n" + "="*60)
    print(f"🎉 三秒数据收集完成！")
    print(f"  📊 总数据点: {len(all_data_records)}")
    print(f"  ⏱️  总时长: 3.0 秒")
    print(f"  📈 平均采样率: {len(all_data_records)/3.0:.1f} Hz")
    print(f"  🎯 目标完成率: {len(all_data_records)/6000*100:.1f}%")

    return all_data_records


# 新增：持续流式预测函数（每次回调都预测）
async def stream_predict_for_duration(processor, displacement_source, velocity_source, duration_seconds=3.0, on_sample=None, target_points_per_second=100):
    """在给定持续时间内持续读取位移和速度，每次有新数据就进行预测。

    参数:
    - processor: 实时处理器
    - displacement_source: 位移数据源
    - velocity_source: 速度数据源
    - duration_seconds: 持续时间（秒）
    - on_sample(record): 可选回调，每产生一个样本时调用
    - target_points_per_second: 目标预测频率（Hz）
    """
    print(f"🚀 开始流式预测，持续 {duration_seconds:.1f}s（每次回调都进行预测）")

    start_time = time.time()
    data_records = []
    last_displacement = None
    last_velocity = None
    prediction_count = 0

    print(f"📊 预测策略: 每次收到新的位移数据就进行预测")
    print(f"📈 目标预测频率: {target_points_per_second} Hz")
    print(f"🎯 预期{duration_seconds:.1f}秒内预测次数: {duration_seconds * target_points_per_second:.0f}次")

    # 持续读取DSA数据并每次都进行预测
    while True:
        current_time = time.time()
        elapsed = current_time - start_time

        if elapsed >= duration_seconds:
            break

        # 持续读取DSA数据
        displacement_data = await displacement_source.read_data()
        velocity_data = await velocity_source.read_data()

        # 更新最新的位移和速度数据
        if displacement_data is not None:
            last_displacement = displacement_data
        if velocity_data is not None:
            last_velocity = velocity_data

        # 每次有新的位移数据就进行预测
        if displacement_data is not None:
            prediction_start = current_time
            prediction = processor.process_single_point(displacement_data)
            processing_time = time.time() - prediction_start
            prediction_count += 1

            if prediction is not None:
                # 获取同步时刻的三种预测（原始LSTM、纠偏后、不经AKF；以及AKF后）
                try:
                    lstm_pred, corrected_pred, akf_pred = processor.get_last_predictions_extended()
                except Exception:
                    lstm_pred, akf_pred = processor.get_last_predictions()
                    corrected_pred = None

                lstm_error = abs(displacement_data - lstm_pred) if lstm_pred is not None else None
                corrected_error = abs(displacement_data - corrected_pred) if corrected_pred is not None else None
                akf_error = abs(displacement_data - akf_pred) if akf_pred is not None else None
                improvement = (lstm_error - akf_error) if (lstm_error is not None and akf_error is not None) else None
                improvement_pct = ((lstm_error - akf_error) / lstm_error * 100.0) if (lstm_error not in (None, 0)) and (akf_error is not None) else None
                imp_vs_corr = (corrected_error - akf_error) if (corrected_error is not None and akf_error is not None) else None
                imp_vs_corr_pct = ((corrected_error - akf_error) / corrected_error * 100.0) if (corrected_error not in (None, 0)) and (akf_error is not None) else None

                record = {
                    'time': elapsed,
                    'displacement': displacement_data,
                    'velocity': last_velocity if last_velocity is not None else 0.0,
                    'prediction': prediction,
                    'lstm_prediction': lstm_pred,
                    'corrected_prediction': corrected_pred,
                    'lstm_error': lstm_error,
                    'corrected_error': corrected_error,
                    'akf_error': akf_error,
                    'error_improvement': improvement,
                    'error_improvement_pct': improvement_pct,
                    'akf_vs_corrected_improvement': imp_vs_corr,
                    'akf_vs_corrected_improvement_pct': imp_vs_corr_pct,
                    'processing_time': processing_time
                }
                data_records.append(record)

                if on_sample is not None:
                    try:
                        on_sample(record)
                    except Exception:
                        pass

                # 显示预测频率统计
                if prediction_count % 100 == 0:  # 每100个预测点显示一次统计
                    actual_freq = prediction_count / elapsed if elapsed > 0 else 0
                    print(f"📊 已完成{prediction_count}次预测，实际频率: {actual_freq:.1f} Hz")

        # 短暂等待，避免CPU占用过高
        await asyncio.sleep(0.00001)  # 10μs等待

    actual_duration = time.time() - start_time
    actual_rate = len(data_records) / actual_duration if actual_duration > 0 else 0
    print(f"✅ 流式预测完成：{len(data_records)} 个点，平均速率 {actual_rate:.1f} Hz")

    return data_records


def save_three_seconds_data(data_records, filename="three_seconds_data.txt"):
    """保存三秒数据到文件"""
    print(f"💾 保存三秒数据到文件: {filename}")

    with open(filename, 'w', encoding='utf-8') as f:
        # 写入表头
        f.write("时间[s]\t位移[μm]\t速度[μm/s]\t预测位移[μm]\t仅LSTM预测[μm]\tLSTM(纠偏后)[μm]\t仅LSTM误差[μm]\tLSTM(纠偏后)误差[μm]\tLSTM+AKF误差[μm]\tAKF相对LSTM改进[μm]\tAKF相对LSTM改进[%]\tAKF相对纠偏后改进[μm]\tAKF相对纠偏后改进[%]\t处理时间[ms]\t数据秒数\n")

        # 写入数据
        for record in data_records:
            # 确定数据属于第几秒
            second_number = int(record['time']) + 1

            f.write(f"{record['time']:.3f}\t"
                   f"{record['displacement']:.6f}\t"
                   f"{record['velocity']:.6f}\t"
                   f"{record['prediction']:.6f}\t"
                   f"{(record.get('lstm_prediction') if record.get('lstm_prediction') is not None else np.nan):.6f}\t"
                   f"{(record.get('corrected_prediction') if record.get('corrected_prediction') is not None else np.nan):.6f}\t"
                   f"{(record.get('lstm_error') if record.get('lstm_error') is not None else np.nan):.6f}\t"
                   f"{(record.get('corrected_error') if record.get('corrected_error') is not None else np.nan):.6f}\t"
                   f"{(record.get('akf_error') if record.get('akf_error') is not None else np.nan):.6f}\t"
                   f"{(record.get('error_improvement') if record.get('error_improvement') is not None else np.nan):.6f}\t"
                   f"{(record.get('error_improvement_pct') if record.get('error_improvement_pct') is not None else np.nan):.2f}\t"
                   f"{(record.get('akf_vs_corrected_improvement') if record.get('akf_vs_corrected_improvement') is not None else np.nan):.6f}\t"
                   f"{(record.get('akf_vs_corrected_improvement_pct') if record.get('akf_vs_corrected_improvement_pct') is not None else np.nan):.2f}\t"
                   f"{record['processing_time']*1000:.3f}\t"
                   f"第{second_number}秒\n")

    # 统计信息
    if data_records:
        displacements = [r['displacement'] for r in data_records]
        velocities = [r['velocity'] for r in data_records]
        predictions = [r['prediction'] for r in data_records]
        processing_times = [r['processing_time']*1000 for r in data_records]

        # 按秒统计数据点数（3~10秒）
        sec_counts = {}
        for s in range(3, 11):  # 3到10秒
            sec_counts[s] = len([r for r in data_records if s <= r['time'] < s+1])

        print(f"✅ 数据已保存到 {filename}")
        print(f"   📊 总数据点数: {len(data_records)}")

        # 动态显示按秒分布
        sec_dist_parts = [f"第{s}秒({sec_counts[s]}个)" for s in range(3, 11) if sec_counts[s] > 0]
        if sec_dist_parts:
            print(f"   📈 按秒分布: {' | '.join(sec_dist_parts)}")

        print(f"   ⏱️  时间范围: {data_records[0]['time']:.3f} ~ {data_records[-1]['time']:.3f} 秒")
        print(f"   📍 位移范围: {min(displacements):.6f} ~ {max(displacements):.6f} μm")
        print(f"   🚀 速度范围: {min(velocities):.6f} ~ {max(velocities):.6f} μm/s")
        print(f"   🎯 预测范围: {min(predictions):.6f} ~ {max(predictions):.6f} μm")
        print(f"   ⚡ 平均处理时间: {np.mean(processing_times):.3f} ms")

    return filename


def create_dsa_real_time_visualization(data, save_prefix="dsa_real_time"):
    """创建DSA实时处理可视化图表"""
    print("生成DSA实时处理可视化图表...")
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('DSA实时数据流处理可视化演示', fontsize=16, fontweight='bold')
    
    real_values = np.array(data['real_values'])
    predictions = np.array(data['predictions'])
    processing_times = np.array(data['processing_times'])
    time_steps = range(len(real_values))
    
    # 计算误差
    errors = np.abs(real_values - predictions)
    
    # 第一张图：实时预测对比
    axes[0, 0].plot(time_steps, real_values, 'b-', linewidth=2, label='DSA真实位移', alpha=0.8)
    axes[0, 0].plot(time_steps, predictions, 'r--', linewidth=2, label='LSTM+AKF预测', alpha=0.8)
    axes[0, 0].set_xlabel('时间步')
    axes[0, 0].set_ylabel('位移 (μm)')
    axes[0, 0].set_title('DSA实时预测对比')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # 添加统计信息
    mae = np.mean(errors)
    rmse = np.sqrt(np.mean(errors**2))
    # 计算10%准确率
    relative_errors = np.abs((real_values - predictions) / real_values) * 100
    accuracy_10percent = np.sum(relative_errors <= 10.0) / len(relative_errors) * 100

    axes[0, 0].text(0.02, 0.98, f'MAE: {mae:.6f}μm\nRMSE: {rmse:.6f}μm\n±10%准确率: {accuracy_10percent:.1f}%',
                    transform=axes[0, 0].transAxes, verticalalignment='top',
                    bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    # 第二张图：预测误差
    axes[0, 1].plot(time_steps, errors, 'g-', linewidth=2, label='预测误差', alpha=0.8)
    axes[0, 1].axhline(y=np.mean(errors), color='orange', linestyle='--', 
                       label=f'平均误差: {np.mean(errors):.6f}μm')
    axes[0, 1].set_xlabel('时间步')
    axes[0, 1].set_ylabel('绝对误差 (μm)')
    axes[0, 1].set_title('DSA实时预测误差')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)
    
    # 第三张图：处理时间分析
    axes[1, 0].plot(time_steps, processing_times * 1000, 'purple', linewidth=2, 
                    label='处理时间', alpha=0.8)
    axes[1, 0].axhline(y=np.mean(processing_times) * 1000, color='red', linestyle='--',
                       label=f'平均时间: {np.mean(processing_times)*1000:.2f}ms')
    axes[1, 0].set_xlabel('时间步')
    axes[1, 0].set_ylabel('处理时间 (ms)')
    axes[1, 0].set_title('DSA实时处理性能')
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)
    
    # 添加性能统计
    avg_time = np.mean(processing_times) * 1000
    max_time = np.max(processing_times) * 1000
    processing_rate = 1.0 / np.mean(processing_times)
    axes[1, 0].text(0.02, 0.98, f'平均: {avg_time:.2f}ms\n最大: {max_time:.2f}ms\n速率: {processing_rate:.1f}次/秒', 
                    transform=axes[1, 0].transAxes, verticalalignment='top',
                    bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
    
    # 第四张图：误差分布直方图
    axes[1, 1].hist(errors, bins=30, alpha=0.7, color='skyblue', edgecolor='black')
    axes[1, 1].axvline(x=np.mean(errors), color='red', linestyle='--', linewidth=2,
                       label=f'平均误差: {np.mean(errors):.6f}μm')
    axes[1, 1].axvline(x=np.median(errors), color='green', linestyle='--', linewidth=2,
                       label=f'中位数误差: {np.median(errors):.6f}μm')
    axes[1, 1].set_xlabel('预测误差 (μm)')
    axes[1, 1].set_ylabel('频次')
    axes[1, 1].set_title('DSA误差分布直方图')
    axes[1, 1].legend()
    axes[1, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    filename = f'{save_prefix}_visualization.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    plt.close()
    print(f"DSA可视化图表已保存到: {filename}")


def create_error_comparison_figures(records, save_prefix="error_comparison"):
    """根据流式记录自动生成误差对比图像（纠偏后LSTM vs LSTM+AKF）。

    生成两张图：
    - {save_prefix}_over_time.png : 误差随时间对比 + 位移对比（真实位移、纠偏后LSTM预测、LSTM+AKF预测）
    - {save_prefix}_hist.png      : 误差分布对比（直方图）+ 关键指标文本
    """
    if not records:
        return None, None

    # 仅保留含有误差字段的有效样本
    valid = [r for r in records if r.get('corrected_error') is not None and r.get('akf_error') is not None]
    if len(valid) < 2:
        return None, None

    times = np.array([r['time'] for r in valid])
    displacements = np.array([r['displacement'] for r in valid])
    corr_pred = np.array([r.get('corrected_prediction', np.nan) for r in valid])
    akf_pred = np.array([r.get('prediction', np.nan) for r in valid])  # 'prediction' 是AKF后的预测
    corr_err = np.array([abs(r['corrected_error']) for r in valid])
    akf_err = np.array([abs(r['akf_error']) for r in valid])

    # 计算改进百分比
    imp_vs_corr_pct = []
    for r in valid:
        ce = abs(r['corrected_error'])
        ae = abs(r['akf_error'])
        if ce > 0:
            imp_vs_corr_pct.append((ce - ae) / ce * 100.0)
        else:
            imp_vs_corr_pct.append(0.0)
    imp_vs_corr_pct = np.array(imp_vs_corr_pct)

    # 指标
    corr_mae = float(np.mean(corr_err))
    akf_mae = float(np.mean(akf_err))
    corr_rmse = float(np.sqrt(np.mean(corr_err**2)))
    akf_rmse = float(np.sqrt(np.mean(akf_err**2)))
    mae_improve = (corr_mae - akf_mae) / corr_mae * 100.0 if corr_mae > 0 else 0.0
    rmse_improve = (corr_rmse - akf_rmse) / corr_rmse * 100.0 if corr_rmse > 0 else 0.0
    avg_imp_vs_corr_pct = float(np.mean(imp_vs_corr_pct))

    # 图1：误差随时间对比 + 位移对比（3行）
    fig1, ax1 = plt.subplots(3, 1, figsize=(14, 12), sharex=True)
    fig1.suptitle('实时误差对比与位移对比（纠偏后LSTM vs LSTM+AKF）', fontsize=14, fontweight='bold')

    # 第一行：位移对比（真实位移、纠偏后LSTM预测、LSTM+AKF预测）
    ax1[0].plot(times, displacements, 'b-', linewidth=2, label='真实位移', alpha=0.8)
    ax1[0].plot(times, corr_pred, 'orange', linewidth=1.5, label='LSTM(纠偏后)预测', alpha=0.7, linestyle='--')
    ax1[0].plot(times, akf_pred, 'g-', linewidth=1.5, label='LSTM+AKF预测', alpha=0.7, linestyle=':')
    ax1[0].set_ylabel('位移 (μm)', fontsize=11)
    ax1[0].grid(True, alpha=0.3)
    ax1[0].legend(fontsize=10, loc='best')
    ax1[0].set_title('位移对比：真实位移 vs 纠偏后LSTM预测 vs LSTM+AKF预测', fontsize=12)

    # 第二行：误差对比
    ax1[1].plot(times, corr_err, 'orange', linewidth=1.5, label='纠偏后LSTM误差 |e|', alpha=0.8)
    ax1[1].plot(times, akf_err, 'g-', linewidth=1.5, label='LSTM+AKF误差 |e|', alpha=0.8)
    ax1[1].set_ylabel('绝对误差 (μm)', fontsize=11)
    ax1[1].grid(True, alpha=0.3)
    ax1[1].legend(fontsize=10)
    ax1[1].set_title(f"MAE(纠偏后LSTM)={corr_mae:.6f} μm, MAE(LSTM+AKF)={akf_mae:.6f} μm, 改进={mae_improve:.2f}%", fontsize=12)

    # 第三行：改进百分比
    ax1[2].plot(times, imp_vs_corr_pct, color='purple', linewidth=1.5, label='AKF相对纠偏后LSTM逐点改进 (%)', alpha=0.8)
    ax1[2].axhline(y=avg_imp_vs_corr_pct, color='purple', linestyle='--', linewidth=2, label=f'平均改进 {avg_imp_vs_corr_pct:.2f}%')
    ax1[2].axhline(y=0, color='black', linestyle='-', linewidth=0.5, alpha=0.5)
    ax1[2].set_xlabel('时间 (s)', fontsize=11)
    ax1[2].set_ylabel('改进 (%)', fontsize=11)
    ax1[2].grid(True, alpha=0.3)
    ax1[2].legend(fontsize=10)

    fig1.tight_layout()
    over_time_png = f"{save_prefix}_over_time.png"
    fig1.savefig(over_time_png, dpi=300, bbox_inches='tight')
    plt.close(fig1)

    # 图2：误差分布直方图
    fig2, ax2 = plt.subplots(1, 1, figsize=(10, 6))
    bins = max(20, int(np.sqrt(len(valid))))
    ax2.hist(corr_err, bins=bins, alpha=0.6, color='orange', edgecolor='black', label='纠偏后LSTM误差 |e|')
    ax2.hist(akf_err, bins=bins, alpha=0.6, color='seagreen', edgecolor='black', label='LSTM+AKF误差 |e|')
    ax2.set_xlabel('绝对误差 (μm)', fontsize=11)
    ax2.set_ylabel('频次', fontsize=11)
    ax2.set_title('误差分布对比（纠偏后LSTM vs LSTM+AKF）', fontsize=12)
    ax2.grid(True, alpha=0.3)
    ax2.legend(fontsize=10)

    # 角标打印关键指标
    text_str = (
        f"纠偏后LSTM:\n"
        f"  MAE={corr_mae:.6f} μm\n"
        f"  RMSE={corr_rmse:.6f} μm\n\n"
        f"LSTM+AKF:\n"
        f"  MAE={akf_mae:.6f} μm\n"
        f"  RMSE={akf_rmse:.6f} μm\n\n"
        f"AKF改进效果:\n"
        f"  MAE改进={mae_improve:.2f}%\n"
        f"  RMSE改进={rmse_improve:.2f}%\n"
        f"  平均逐点改进={avg_imp_vs_corr_pct:.2f}%"
    )
    ax2.text(0.97, 0.97, text_str, transform=ax2.transAxes, va='top', ha='right',
             bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8), fontsize=10)
    fig2.tight_layout()
    hist_png = f"{save_prefix}_hist.png"
    fig2.savefig(hist_png, dpi=300, bbox_inches='tight')
    plt.close(fig2)

    print(f"📈 已生成误差对比图: {over_time_png}")
    print(f"📈 已生成误差分布图: {hist_png}")
    return over_time_png, hist_png

async def main():
    """主函数"""
    print("="*60)
    print("DSA实时数据流 + LSTM+AKF 预测演示")
    print("流程: 收集300000个数据点(每10个点取1个)训练 → 训练模型 → 预测10秒位移")
    print("训练集: 30000个数据点（每10个原始数据点取1个）")
    print("数据源: DSA数据收集器 (传递最新位移和速度数据)")
    print("预测策略: 每次回调传递一组最新的位移和速度数据给预测模型")
    print("数据传递: 只传递最新值，不传递全部数据")
    print("显示: 3~10秒预测结果")
    print("="*60)
    
    # 检查DLL文件
    dll_path = r"SDK发布20200723\x64\DSANetSDK.dll"
    if not os.path.exists(dll_path):
        print(f"错误：找不到DLL文件 {dll_path}")
        return
    
    # 1. 收集训练数据
    print("步骤1: 收集训练数据...")
    bridge = RealTimeVisualizationBridge(dll_path)

    # 启动数据收集来获取训练数据（训练模式，不启用连续模式）
    if not await bridge.start_displacement_visualization(for_training=True):
        print("启动DSA数据收集失败")
        return

    displacement_source = bridge.get_displacement_source()

    # 等待训练数据收集完成（300000个原始数据点，约30秒）
    print("等待收集300000个原始数据点（每10个点取1个，得到30000个训练数据点）...")
    training_collected = False
    check_count = 0

    while not training_collected and check_count < 35:  # 最多等待35秒（30秒数据收集 + 5秒缓冲）
        await asyncio.sleep(1.0)
        check_count += 1

        # 检查训练数据收集状态
        try:
            status = displacement_source.collector.get_training_data_status()
            elapsed_time = status.get('elapsed_time', 0.0)
            expected_training_points = status.get('expected_training_points', 0)
            print(f"训练数据收集进度: {status['count']}/300000 个原始数据点 ({status['progress']:.1f}%) - 预期训练点数: {expected_training_points} - 已用时: {elapsed_time:.1f}s")
            training_collected = status['collected']
        except Exception as e:
            # 如果方法不存在，直接检查收集器运行状态
            current_count = len(displacement_source.collector.training_data)
            is_running = displacement_source.collector.is_running
            training_collected = not is_running  # 如果停止运行，说明收集完成
            expected_training_points = current_count // 100
            print(f"训练数据收集进度: {current_count} 个原始数据点 - 预期训练点数: {expected_training_points} - 运行状态: {'收集中' if is_running else '已完成'}")

    if not training_collected:
        print("训练数据收集超时，程序退出")
        bridge.stop_all()
        return

    print("✅ 训练数据收集完成！")

    # 停止当前的数据收集
    bridge.stop_all()
    await asyncio.sleep(2.0)  # 等待完全停止

    # 2. 使用新收集的数据训练模型
    print("步骤2: 使用新收集的训练数据训练LSTM模型...")

    # 读取新收集的训练数据检查范围
    try:
        train_df = pd.read_csv('v1.txt', sep='\t', encoding='utf-8')
        train_data = train_df.iloc[:, 1].values
        train_min, train_max = train_data.min(), train_data.max()
        print(f"📊 新训练数据范围: {train_min:.6f} ~ {train_max:.6f} μm")
        print(f"📊 新训练数据点数: {len(train_data)}")

        if len(train_data) < 3000:
            print(f"⚠️  训练数据不足3000个点，实际收集到{len(train_data)}个点，可能影响训练效果")
        elif len(train_data) >= 3000:
            print(f"✅ 训练数据收集完成，使用{len(train_data)}个点进行训练")
        else:
            print(f"✅ 训练数据收集完成，使用{len(train_data)}个点进行训练")

    except Exception as e:
        print(f"读取训练数据失败: {e}")
        return

    # 导入训练模块
    from real_time_train import train_displacement_model
    from real_time_train import save_model as save_trained_model

    # 使用新数据训练模型
    print("🚀 开始训练新模型...")
    model, scaler = train_displacement_model(
        data_file='v1.txt',
        window_size=25,  # 窗口大小
        hidden_size=128,  # 隐藏层大小适应30000个数据点
        learning_rate=0.0005,  # 降低学习率以适应更多数据
        epochs=150,  # 增加训练轮数以充分利用更多数据
        train_points=len(train_data)  # 使用实际收集到的数据点数
    )

    if model is None:
        print("❌ 模型训练失败，程序退出")
        return

    # 检查新训练的标准化器范围
    if scaler is not None:
        scaler_min, scaler_max = scaler.data_min_[0], scaler.data_max_[0]
        print(f"📊 新标准化器拟合范围: {scaler_min:.6f} ~ {scaler_max:.6f} μm")
        print(f"📊 新标准化器输出范围: {scaler.feature_range}")

        # 验证标准化器与训练数据的一致性
        if abs(scaler_min - train_min) < 1e-6 and abs(scaler_max - train_max) < 1e-6:
            print("✅ 标准化器与训练数据范围完全一致！")
        else:
            print("⚠️  标准化器与训练数据范围不一致，可能存在问题")
    else:
        print("❌ 标准化器创建失败")
        return

    print("✅ 使用新数据的模型训练完成！")

    # 保存本次训练得到的模型与标准化器，供后续离线对比脚本加载
    try:
        saved = save_trained_model(model, scaler, model_path='integrated_model.pth', scaler_path='integrated_scaler.pkl')
        if saved:
            print("💾 已保存集成演示训练得到的模型: integrated_model.pth 与 integrated_scaler.pkl")
        else:
            print("⚠️ 集成演示模型保存失败（将继续运行，但对比脚本无法直接复用本次模型）")
    except Exception as e:
        print(f"⚠️ 保存集成演示模型异常: {e}")
    
    # 3. 创建DSA数据源用于预测（位移和速度）
    print("步骤3: 创建DSA数据源用于预测...")
    bridge = RealTimeVisualizationBridge(dll_path)

    # 启动位移数据收集
    if not await bridge.start_displacement_visualization():
        print("启动DSA数据收集失败")
        return
    displacement_source = bridge.get_displacement_source()

    # 启动速度数据收集
    if not await bridge.start_velocity_visualization():
        print("启动DSA速度数据收集失败")
        bridge.stop_all()

        return
    velocity_source = bridge.get_velocity_source()

    # 4. 等待数据收集稳定并收集初始化数据
    print("步骤4: 收集初始化数据...")
    print("等待DSA数据流稳定...")
    await asyncio.sleep(3.0)  # 等待3秒让数据流稳定

    historical_data = []
    max_attempts = 5000  # 增加尝试次数
    attempt_count = 0

    print("开始收集初始化数据点...")
    # 收集25个数据点用于初始化
    while len(historical_data) < 25 and attempt_count < max_attempts:
        data = await displacement_source.read_data()
        if data is not None:
            historical_data.append(data)
            if len(historical_data) % 5 == 0:  # 每5个数据点显示一次进度
                print(f"已收集 {len(historical_data)}/25 个初始化数据点")

        # 每1000次尝试检查一次状态
        if attempt_count % 1000 == 0 and attempt_count > 0:
            if hasattr(displacement_source, 'collector') and displacement_source.collector:
                status = displacement_source.collector.get_collection_status()
                print(f"数据收集器状态: 运行={status['is_running']}, 总数据点={status['total_data_points']}, 最新位移={status['latest_displacement']}")

        attempt_count += 1
        await asyncio.sleep(0.01)  # 增加等待时间到10ms

    if len(historical_data) < 25:
        print(f"收集的初始化数据不足: {len(historical_data)} < 25")
        print(f"尝试了 {attempt_count} 次，可能的原因：")
        print("  1. DSA设备未正确连接")
        print("  2. 数据回调频率太低")
        print("  3. 数据收集器配置问题")
        bridge.stop_all()
        return

    print(f"✅ 成功收集到 {len(historical_data)} 个初始化数据点")

    # 5. 创建实时处理器（启用AKF+偏移校正）
    print("步骤5: 创建LSTM+AKF+偏移校正实时处理器...")
    processor = RealTimeStreamProcessor(
        model=model,
        scaler=scaler,
        window_size=25,
        buffer_size=10000,
        enable_akf=True,  # 启用AKF优化
        enable_bias_correction=True  # 启用偏移校正
    )

    # 初始化处理器
    processor.initialize_with_historical_data(historical_data)
    processor.enable_outlier_detection = False  # 关闭异常值检测

    # 6. 训练完成后开始计时，收集前三秒数据
    print("步骤6: 训练完成，开始正式计时...")

    # 收集三秒的位移、速度和预测数据（改为实时读取即预测的流式方式）
    # 可选：每100个样本打印一次轻量日志
    sample_counter = {'count': 0}
    def on_sample(record):
        sample_counter['count'] += 1
        if sample_counter['count'] % 100 == 0:
            lstm_err = record.get('lstm_error')
            corr_err = record.get('corrected_error')
            akf_err = record.get('akf_error')
            imp_pct = record.get('error_improvement_pct')
            if lstm_err is not None and akf_err is not None and imp_pct is not None:
                print(
                    f"  [实时] t={record['time']:.3f}s, 位移={record['displacement']:.6f} μm, "
                    f"LSTM={record.get('lstm_prediction'):.6f} μm, 纠偏后={record.get('corrected_prediction') if record.get('corrected_prediction') is not None else float('nan'):.6f} μm, AKF={record['prediction']:.6f} μm, "
                    f"|e|_LSTM={lstm_err:.6f} μm, |e|_纠偏后={corr_err if corr_err is not None else float('nan'):.6f} μm, |e|_AKF={akf_err:.6f} μm, 改进={imp_pct:.2f}%, 耗时={record['processing_time']*1000:.2f} ms"
                )
            else:
                print(f"  [实时] t={record['time']:.3f}s, 位移={record['displacement']:.6f} μm, 预测={record['prediction']:.6f} μm, 耗时={record['processing_time']*1000:.2f} ms")

    # 设置预测持续时长为10秒
    prediction_duration = 10
    # 设置目标预测频率（每次回调都预测，预期频率约为DSA回调频率）
    target_prediction_frequency = 1000  # 目标1000Hz预测频率（基于DSA回调频率）

    three_seconds_data = await stream_predict_for_duration(
        processor=processor,
        displacement_source=displacement_source,
        velocity_source=velocity_source,
        duration_seconds=prediction_duration,
        on_sample=on_sample,
        target_points_per_second=target_prediction_frequency  # 目标预测频率
    )

    # 仅保留3秒以后的数据（显示3~10秒的结果）
    filtered_data = [r for r in three_seconds_data if r['time'] >= 3.0]
    effective_duration = max(0.0, prediction_duration - 3.0)

    # 7. 保存三秒数据到文件
    print("步骤7: 保存三秒数据到文件...")
    data_filename = save_three_seconds_data(filtered_data, "three_seconds_data.txt")

    # 8. 生成统计结果
    print("\n" + "="*60)
    print("📊 DSA按秒收集实时预测统计结果")
    print("="*60)

    if len(filtered_data) > 0:
        displacements = np.array([r['displacement'] for r in filtered_data])
        predictions = np.array([r['prediction'] for r in filtered_data])
        velocities = np.array([r['velocity'] for r in filtered_data])
        processing_times = np.array([r['processing_time'] for r in filtered_data])

        # 按秒统计（3~10秒）
        per_sec_counts = []
        for s in range(3, int(prediction_duration)+1):
            cnt = len([r for r in filtered_data if s <= r['time'] < s + 1])
            per_sec_counts.append(cnt)

        errors = np.abs(displacements - predictions)
        mae = np.mean(errors)
        rmse = np.sqrt(np.mean(errors**2))

        relative_errors = np.abs((displacements - predictions) / displacements) * 100
        accuracy_10percent = np.sum(relative_errors <= 10.0) / len(relative_errors) * 100

        print(f"🕐 数据收集方式: 训练完成后开始计时收集")
        print(f"⏱️  数据收集时长: {effective_duration:.1f} 秒（显示3~10秒区间）")
        print(f"📊 总数据点数: {len(filtered_data)}")
        # 打印动态按秒分布（3~10秒）
        dist_str = " | ".join([f"第{s}秒({per_sec_counts[idx]}个)" for idx, s in enumerate(range(3, int(prediction_duration)+1))])
        print(f"📈 按秒分布: {dist_str}")
        avg_rate_denominator = effective_duration if effective_duration > 0 else 1.0
        print(f"🎯 平均采样率: {len(filtered_data)/avg_rate_denominator:.1f} Hz (目标: 100Hz)")
        print(f"⚡ 平均处理时间: {np.mean(processing_times)*1000:.2f} ms")
        print(f"🚀 处理速率: {1.0/np.mean(processing_times):.1f} 次/秒")
        print(f"📍 位移MAE: {mae:.6f} μm (AKF优化后)")
        print(f"📍 位移RMSE: {rmse:.6f} μm (AKF优化后)")
        print(f"🎯 ±10%准确率: {accuracy_10percent:.1f}% (AKF优化后)")

        # 额外诊断：列出计数为0的整秒区间（3~10秒）
        zero_secs = [s for idx, s in enumerate(range(3, int(prediction_duration)+1)) if per_sec_counts[idx] == 0]
        if len(zero_secs) > 0:
            print(f"⚠️ 以下整秒区间未采集到任何样本：{', '.join([f'第{s}秒' for s in zero_secs])}")
            print("   可能原因：数据源在该秒无新数据、读取频率不足或DLL缓冲区读取为空窗。")

        # 获取详细的LSTM vs AKF统计
        stats = processor.get_statistics()
        if stats and 'akf_enabled' in stats and stats['akf_enabled']:
            print(f"\n🔍 LSTM vs AKF+偏移校正 对比分析:")
            print(f"   LSTM原始MAE: {stats['lstm_mae']:.6f} μm")
            print(f"   AKF优化MAE:  {stats['akf_mae']:.6f} μm")
            print(f"   MAE改进率:   {stats['mae_improvement']:.2f}%")
            print(f"   LSTM原始RMSE: {stats['lstm_rmse']:.6f} μm")
            print(f"   AKF优化RMSE:  {stats['akf_rmse']:.6f} μm")
            print(f"   RMSE改进率:   {stats['rmse_improvement']:.2f}%")
            print(f"   LSTM ±10%准确率: {stats['lstm_accuracy_10percent']:.1f}%")
            print(f"   AKF ±10%准确率:  {stats['akf_accuracy_10percent']:.1f}%")

            # 基于实时流记录的逐点改进率（仅统计t≥2s区间）
            lstm_err_list = [r.get('lstm_error') for r in filtered_data if r.get('lstm_error') is not None]
            akf_err_list = [r.get('akf_error') for r in filtered_data if r.get('akf_error') is not None]
            imp_pct_list = [r.get('error_improvement_pct') for r in filtered_data if r.get('error_improvement_pct') is not None]
            if lstm_err_list and akf_err_list:
                print("   —— 实时逐点误差对比 ——")
                print(f"   平均|误差| LSTM: {np.mean(lstm_err_list):.6f} μm, AKF: {np.mean(akf_err_list):.6f} μm")
            if imp_pct_list:
                print(f"   平均误差改进: {np.mean(imp_pct_list):.2f}%")

            # 显示偏移校正信息
            if stats.get('bias_correction_enabled', False):
                print(f"\n🔧 偏移校正信息:")
                print(f"   偏移校正值: {stats['bias_correction_value']:.6f} μm")
                print(f"   校正状态: {'已激活' if stats['bias_calculated'] else '计算中'}")
                print(f"   样本数量: {stats['bias_samples_count']}")
                if stats['bias_calculated']:
                    print(f"   ✅ 偏移校正已生效，预测精度应显著提升")
        print(f"📊 位移范围: {np.min(displacements):.6f} ~ {np.max(displacements):.6f} μm")
        print(f"🚀 速度范围: {np.min(velocities):.6f} ~ {np.max(velocities):.6f} μm/s")

        print(f"\n💾 数据已保存到: {data_filename}")

        # 10. 自动生成误差对比图像
        print("\n步骤9: 生成误差对比图像...")
        create_error_comparison_figures(filtered_data, save_prefix="error_comparison")

        # 11. 调用 plot_displacement.py 生成位移对比与误差分析图
        print("\n步骤10: 调用plot_displacement生成位移对比/误差分析图...")
        try:
            print(f"  📂 尝试加载数据文件: {data_filename}")
            data_df = plot_load_data(data_filename)
            if data_df is not None and len(data_df) > 0:
                print(f"  ✅ 数据加载成功，共 {len(data_df)} 行")
                print(f"  📋 数据列: {list(data_df.columns)}")

                # 位移对比图
                print("  📊 生成位移对比图...")
                fig_cmp = plot_displacement_comparison(data_df)
                plt.savefig('displacement_comparison.png', dpi=300, bbox_inches='tight')
                plt.close(fig_cmp)
                print("  ✅ 位移对比图已保存: displacement_comparison.png")

                # 误差分析图
                print("  📊 生成误差分析图...")
                fig_err = plot_error_analysis(data_df)
                plt.savefig('error_analysis.png', dpi=300, bbox_inches='tight')
                plt.close(fig_err)
                print("  ✅ 误差分析图已保存: error_analysis.png")

                # 控制台输出关键指标
                print("  📊 计算预测精度指标...")
                metrics = plot_calculate_metrics(data_df)
                print("\n📊 预测精度指标(基于AKF预测列):")
                print(f"  MAE: {metrics['MAE']:.6f} μm, RMSE: {metrics['RMSE']:.6f} μm, 相关系数: {metrics['Correlation']:.6f}")
            else:
                print("  ⚠️ 数据加载失败或数据为空，跳过图像生成。")
        except Exception as e:
            print(f"  ❌ 生成plot_displacement图像时出错: {e}")
            import traceback
            traceback.print_exc()
    
    # 9. 停止数据收集
    print("\n步骤8: 停止数据收集...")
    bridge.stop_all()

    print("\n" + "="*60)
    print("✅ DSA按秒收集实时预测演示完成！")
    print("📁 生成的文件:")
    print(f"  📄 {data_filename} - 按秒收集的位移、速度和预测数据")
    print("  📄 v1.txt - 训练数据文件")
    print("\n🎯 主要成果:")
    print("  ✅ 收集300000个原始数据点（每10个点取1个，得到30000个训练数据点）")
    print("  ✅ 使用30000个训练数据点训练LSTM模型")
    print("  ✅ 训练完成后正式开始计时")
    print("  ✅ 收集10秒位移、速度和预测数据，显示3~10秒结果")
    print("  ✅ 每次回调传递最新位移和速度数据给预测模型")
    print("  ✅ 数据传递优化：只传递最新值，不传递全部数据")
    print("  ✅ 实时预测策略：每次收到新位移数据就立即预测")
    print("  ✅ 高效数据流，减少内存占用和传输开销")
    print("  ✅ 实时预测性能优异")
    print("="*60)


if __name__ == "__main__":
    asyncio.run(main())
