"""
简化的数据流测试
测试DSA数据收集器 -> 数据桥接 -> 实时预测的数据传递流程
不涉及复杂的机器学习依赖
"""

import asyncio
import time
from real_time_data_bridge import DSADataSource, RealTimeVisualizationBridge

async def test_integrated_data_flow():
    """测试完整的数据流：收集 -> 桥接 -> 预测"""
    dll_path = r"SDK发布20200723\x64\DSANetSDK.dll"
    
    print("="*70)
    print("DSA数据流 -> 实时预测 集成测试")
    print("="*70)
    
    try:
        # 1. 创建数据桥接器
        print("步骤1: 创建实时可视化桥接器...")
        bridge = RealTimeVisualizationBridge(dll_path)
        
        # 2. 启动位移数据收集
        print("步骤2: 启动位移数据收集...")
        if not await bridge.start_displacement_visualization():
            print("❌ 启动位移数据收集失败")
            return False
        displacement_source = bridge.get_displacement_source()
        
        # 3. 启动速度数据收集
        print("步骤3: 启动速度数据收集...")
        if not await bridge.start_velocity_visualization():
            print("❌ 启动速度数据收集失败")
            bridge.stop_all()
            return False
        velocity_source = bridge.get_velocity_source()
        
        # 4. 收集初始化数据（模拟LSTM需要的25个数据点）
        print("步骤4: 收集初始化数据...")
        print("等待DSA数据流稳定...")
        await asyncio.sleep(3.0)
        
        historical_data = []
        max_attempts = 2000
        attempt_count = 0
        
        print("开始收集初始化数据点...")
        while len(historical_data) < 25 and attempt_count < max_attempts:
            data = await displacement_source.read_data()
            if data is not None:
                historical_data.append(data)
                if len(historical_data) % 5 == 0:
                    print(f"已收集 {len(historical_data)}/25 个初始化数据点")
            
            attempt_count += 1
            await asyncio.sleep(0.01)
        
        if len(historical_data) < 25:
            print(f"❌ 收集的初始化数据不足: {len(historical_data)} < 25")
            bridge.stop_all()
            return False
        
        print(f"✅ 成功收集到 {len(historical_data)} 个初始化数据点")
        print(f"   数据范围: {min(historical_data):.6f} ~ {max(historical_data):.6f} μm")
        
        # 5. 模拟实时预测流程
        print("步骤5: 开始模拟实时预测流程...")
        prediction_count = 0
        start_time = time.time()
        target_duration = 10.0  # 运行10秒
        
        last_displacement = None
        last_velocity = None
        
        print("开始实时数据处理...")
        while time.time() - start_time < target_duration:
            current_time = time.time()
            
            # 读取最新数据
            displacement_data = await displacement_source.read_data()
            velocity_data = await velocity_source.read_data()
            
            # 更新最新数据
            if displacement_data is not None:
                last_displacement = displacement_data
            if velocity_data is not None:
                last_velocity = velocity_data
            
            # 每次有新的位移数据就进行"预测"（这里只是模拟）
            if displacement_data is not None:
                prediction_start = current_time
                
                # 模拟预测计算（简单的线性预测）
                if last_velocity is not None:
                    # 简单预测：当前位移 + 速度 * 时间步长
                    time_step = 0.001  # 1ms预测步长
                    predicted_displacement = displacement_data + last_velocity * time_step
                else:
                    predicted_displacement = displacement_data
                
                processing_time = time.time() - prediction_start
                prediction_count += 1
                
                # 每50次预测显示一次结果
                if prediction_count % 50 == 0:
                    elapsed = current_time - start_time
                    print(f"预测 #{prediction_count}: 位移={displacement_data:.6f}μm, "
                          f"速度={last_velocity:.6f}μm/s, 预测={predicted_displacement:.6f}μm, "
                          f"处理时间={processing_time*1000:.2f}ms, 运行时间={elapsed:.1f}s")
            
            # 短暂等待
            await asyncio.sleep(0.001)
        
        # 6. 统计结果
        total_time = time.time() - start_time
        avg_prediction_rate = prediction_count / total_time
        
        print(f"\n步骤6: 测试完成统计")
        print(f"   运行时间: {total_time:.2f} 秒")
        print(f"   总预测次数: {prediction_count}")
        print(f"   平均预测频率: {avg_prediction_rate:.1f} Hz")
        print(f"   最终位移: {last_displacement:.6f} μm")
        print(f"   最终速度: {last_velocity:.6f} μm/s")
        
        # 7. 停止数据收集
        print("步骤7: 停止数据收集...")
        bridge.stop_all()
        
        # 判断测试是否成功
        if prediction_count > 100 and avg_prediction_rate > 50:
            print("\n🎉 集成测试成功！")
            print("   ✅ 数据收集正常")
            print("   ✅ 数据桥接正常")
            print("   ✅ 实时预测流程正常")
            print("   ✅ 预测频率满足要求")
            return True
        else:
            print("\n⚠️  测试部分成功，但性能可能不足")
            print(f"   预测次数: {prediction_count} (期望 > 100)")
            print(f"   预测频率: {avg_prediction_rate:.1f} Hz (期望 > 50 Hz)")
            return False
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    print("开始DSA数据流集成测试...\n")
    
    result = await test_integrated_data_flow()
    
    print("\n" + "="*70)
    print("测试总结")
    print("="*70)
    
    if result:
        print("🎉 所有测试通过！")
        print("\n✅ 数据流集成测试成功，系统已准备好进行实际的LSTM+AKF预测")
        print("✅ 每次DSA回调都能成功传递最新的位移和速度数据给预测模型")
        print("✅ 预测频率满足实时要求")
        print("\n📋 下一步建议：")
        print("   1. 安装机器学习依赖包 (numpy, torch, sklearn等)")
        print("   2. 运行完整的 integrated_real_time_demo.py")
        print("   3. 验证LSTM+AKF预测模型的实际性能")
    else:
        print("⚠️  测试存在问题")
        print("\n🔧 建议检查：")
        print("   1. DSA设备连接状态")
        print("   2. 数据回调频率设置")
        print("   3. 系统性能和资源占用")

if __name__ == "__main__":
    asyncio.run(main())
